#!/usr/bin/env python3
"""
Quick Start Script for MCP Server Setup
Automates the entire setup process
"""

import subprocess
import sys
import os
import time
import requests
from pathlib import Path

def run_command(command, description, check=True):
    """Run a shell command with error handling"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False

def check_prerequisites():
    """Check if prerequisites are installed"""
    print("🔍 Checking prerequisites...")
    
    # Check Python
    try:
        import sys
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            print(f"✅ Python {python_version.major}.{python_version.minor} found")
        else:
            print(f"❌ Python 3.8+ required, found {python_version.major}.{python_version.minor}")
            return False
    except:
        print("❌ Python not found")
        return False
    
    # Check UV
    uv_check = run_command("uv --version", "Checking UV", check=False)
    if not uv_check:
        print("⚠️  UV not found, will attempt to install...")
        return "install_uv"
    
    return True

def install_uv():
    """Install UV package manager"""
    print("📦 Installing UV package manager...")
    if os.name == 'nt':  # Windows
        return run_command("powershell -c \"irm https://astral.sh/uv/install.ps1 | iex\"", "Installing UV on Windows")
    else:  # Unix-like
        return run_command("curl -LsSf https://astral.sh/uv/install.sh | sh", "Installing UV on Unix")

def install_mcp_servers():
    """Install AWS MCP servers"""
    servers = [
        "awslabs-cost-explorer-mcp-server@latest",
        "awslabs-cfn-mcp-server@latest",
        "awslabs-aws-pricing-mcp-server@latest"
    ]
    
    for server in servers:
        if not run_command(f"uv tool install {server}", f"Installing {server}"):
            return False
    
    return True

def install_python_deps():
    """Install Python dependencies"""
    deps = [
        "fastapi", "uvicorn", "boto3", "python-dotenv", 
        "pydantic", "mcp", "requests"
    ]
    
    return run_command(f"pip install {' '.join(deps)}", "Installing Python dependencies")

def setup_env_file():
    """Set up environment file"""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        print("📝 Creating .env file from template...")
        try:
            env_file.write_text(env_example.read_text())
            print("✅ .env file created")
            print("⚠️  Please edit .env file with your AWS credentials!")
            return True
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("⚠️  No .env.example found, skipping .env creation")
        return True

def check_aws_config():
    """Check AWS configuration"""
    print("🔍 Checking AWS configuration...")
    
    # Check for AWS CLI
    aws_cli_check = run_command("aws --version", "Checking AWS CLI", check=False)
    
    # Check for credentials
    aws_profile = os.getenv("AWS_PROFILE", "default")
    creds_check = run_command(f"aws configure list --profile {aws_profile}", "Checking AWS credentials", check=False)
    
    if not aws_cli_check or not creds_check:
        print("⚠️  AWS not configured. Please run 'aws configure' or set environment variables in .env")
        return False
    
    print("✅ AWS configuration found")
    return True

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting FastAPI server...")
    print("   Server will start in the background...")
    print("   Check the terminal for startup logs")
    
    # Start server in background
    try:
        subprocess.Popen([sys.executable, "main.py"], 
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Check if server is running
        try:
            response = requests.get("http://localhost:8000/", timeout=5)
            if response.status_code == 200:
                print("✅ Server started successfully!")
                return True
        except:
            pass
        
        print("⚠️  Server may still be starting up...")
        return True
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return False

def main():
    """Main setup function"""
    print("🎯 MCP Server Quick Start Setup")
    print("=" * 40)
    
    # Check prerequisites
    prereq_result = check_prerequisites()
    if prereq_result == "install_uv":
        if not install_uv():
            print("❌ Failed to install UV. Please install manually.")
            sys.exit(1)
    elif not prereq_result:
        print("❌ Prerequisites check failed")
        sys.exit(1)
    
    # Install MCP servers
    if not install_mcp_servers():
        print("❌ Failed to install MCP servers")
        sys.exit(1)
    
    # Install Python dependencies
    if not install_python_deps():
        print("❌ Failed to install Python dependencies")
        sys.exit(1)
    
    # Setup environment file
    setup_env_file()
    
    # Check AWS configuration
    aws_configured = check_aws_config()
    
    print("\n🎉 Setup completed!")
    print("=" * 40)
    
    if aws_configured:
        print("✅ All prerequisites met")
        
        # Ask if user wants to start server
        try:
            start_now = input("\n🚀 Start the server now? (y/N): ").lower().strip()
            if start_now in ['y', 'yes']:
                start_server()
                print("\n📋 Next steps:")
                print("   1. Check server status: curl http://localhost:8000/servers")
                print("   2. List tools: curl http://localhost:8000/tools")
                print("   3. Test chat: curl -X POST http://localhost:8000/chat -H 'Content-Type: application/json' -d '{\"message\": \"Hello!\"}'")
            else:
                print("\n📋 To start the server manually:")
                print("   python main.py")
        except KeyboardInterrupt:
            print("\n👋 Setup completed. Run 'python main.py' to start the server.")
    else:
        print("⚠️  Please configure AWS credentials before starting:")
        print("   1. Run: aws configure")
        print("   2. Or edit .env file with your credentials")
        print("   3. Then run: python main.py")

if __name__ == "__main__":
    main()
