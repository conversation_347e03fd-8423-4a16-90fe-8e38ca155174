#!/usr/bin/env python3
"""
Server Configuration Loader for MCP Client
Loads server configurations from server_config.json and adds them to the running MCP client.
"""

import json
import requests
import sys
import os
from typing import Dict, List, Any
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
CONFIG_FILE = "server_config.json"

class ServerLoader:
    def __init__(self, api_base_url: str = API_BASE_URL):
        self.api_base_url = api_base_url.rstrip('/')
        
    def load_config(self, config_file: str = CONFIG_FILE) -> Dict[str, Any]:
        """Load server configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            print(f"❌ Configuration file {config_file} not found")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in {config_file}: {e}")
            sys.exit(1)
    
    def check_api_health(self) -> bool:
        """Check if the MCP API is running"""
        try:
            response = requests.get(f"{self.api_base_url}/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    print("✅ MCP API is running and healthy")
                    return True
            print(f"❌ MCP API returned unexpected response: {response.status_code}")
            return False
        except requests.exceptions.ConnectionError:
            print(f"❌ Cannot connect to MCP API at {self.api_base_url}")
            print("   Make sure main.py is running with: python main.py")
            return False
        except Exception as e:
            print(f"❌ Error checking API health: {e}")
            return False
    
    def add_server(self, server_config: Dict[str, Any]) -> bool:
        """Add a single server to the MCP client"""
        try:
            # Merge environment variables with server env
            env_vars = server_config.get("env", {})
            
            # Add AWS credentials from environment if not specified
            if not env_vars.get("AWS_ACCESS_KEY_ID") and os.getenv("AWS_ACCESS_KEY_ID"):
                env_vars["AWS_ACCESS_KEY_ID"] = os.getenv("AWS_ACCESS_KEY_ID")
            if not env_vars.get("AWS_SECRET_ACCESS_KEY") and os.getenv("AWS_SECRET_ACCESS_KEY"):
                env_vars["AWS_SECRET_ACCESS_KEY"] = os.getenv("AWS_SECRET_ACCESS_KEY")
            if not env_vars.get("AWS_SESSION_TOKEN") and os.getenv("AWS_SESSION_TOKEN"):
                env_vars["AWS_SESSION_TOKEN"] = os.getenv("AWS_SESSION_TOKEN")
            
            # Clean up empty environment variables
            env_vars = {k: v for k, v in env_vars.items() if v}
            
            payload = {
                "name": server_config["name"],
                "command": server_config["command"],
                "args": server_config.get("args", []),
                "env": env_vars,
                "description": server_config.get("description", ""),
                "enabled": server_config.get("enabled", True)
            }
            
            print(f"🔄 Adding server: {server_config['name']}")
            response = requests.post(
                f"{self.api_base_url}/servers",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ Successfully added server: {server_config['name']}")
                return True
            else:
                error_detail = response.json().get("detail", "Unknown error") if response.headers.get("content-type", "").startswith("application/json") else response.text
                print(f"❌ Failed to add server {server_config['name']}: {error_detail}")
                return False
                
        except Exception as e:
            print(f"❌ Error adding server {server_config['name']}: {e}")
            return False
    
    def list_servers(self) -> Dict[str, Any]:
        """List currently configured servers"""
        try:
            response = requests.get(f"{self.api_base_url}/servers", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to list servers: {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ Error listing servers: {e}")
            return {}
    
    def load_all_servers(self, config_file: str = CONFIG_FILE, enabled_only: bool = True) -> None:
        """Load all servers from configuration file"""
        print(f"📋 Loading server configurations from {config_file}")
        
        # Check API health first
        if not self.check_api_health():
            return
        
        # Load configuration
        config = self.load_config(config_file)
        servers = config.get("servers", [])
        
        if not servers:
            print("❌ No servers found in configuration")
            return
        
        # Filter enabled servers if requested
        if enabled_only:
            servers = [s for s in servers if s.get("enabled", True)]
            print(f"📊 Found {len(servers)} enabled servers to load")
        else:
            print(f"📊 Found {len(servers)} total servers to load")
        
        # Add each server
        success_count = 0
        for server in servers:
            if self.add_server(server):
                success_count += 1
            time.sleep(1)  # Brief delay between server additions
        
        print(f"\n📈 Summary: {success_count}/{len(servers)} servers added successfully")
        
        # Show final server status
        print("\n📋 Current server status:")
        servers_status = self.list_servers()
        for name, info in servers_status.items():
            status_emoji = "✅" if info["status"] == "connected" else "❌" if info["status"] == "error" else "🔄"
            print(f"  {status_emoji} {name}: {info['status']} ({info['tools_count']} tools)")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Load MCP server configurations")
    parser.add_argument("--config", "-c", default=CONFIG_FILE, help="Configuration file path")
    parser.add_argument("--api-url", "-u", default=API_BASE_URL, help="MCP API base URL")
    parser.add_argument("--include-disabled", "-d", action="store_true", help="Include disabled servers")
    parser.add_argument("--list-only", "-l", action="store_true", help="Only list current servers")
    
    args = parser.parse_args()
    
    loader = ServerLoader(args.api_url)
    
    if args.list_only:
        print("📋 Current servers:")
        servers = loader.list_servers()
        if servers:
            for name, info in servers.items():
                status_emoji = "✅" if info["status"] == "connected" else "❌" if info["status"] == "error" else "🔄"
                print(f"  {status_emoji} {name}: {info['status']} ({info['tools_count']} tools)")
        else:
            print("  No servers configured")
    else:
        loader.load_all_servers(args.config, not args.include_disabled)

if __name__ == "__main__":
    main()
