# MCP Client API with AWS Integration

A FastAPI-based client for AWS MCP (Model Context Protocol) servers with automatic configuration and Bedrock integration.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
python quick_start.py
```

### Option 2: Manual Setup
1. Follow the detailed [Setup Guide](SETUP_GUIDE.md)
2. Start the server: `python main.py`

## 📋 Features

- **Auto-configuration**: Servers are configured automatically on startup
- **Multiple AWS MCP Servers**: Cost Explorer, CloudFormation, and Pricing
- **Bedrock Integration**: Chat with <PERSON> using AWS tools
- **RESTful API**: Full REST API for server and tool management
- **Health Monitoring**: Built-in health checks and debugging endpoints

## 🛠️ Available MCP Servers

| Server | Description | Tools |
|--------|-------------|-------|
| **Cost Explorer** | AWS cost analysis and billing insights | Cost reports, spending analysis |
| **CloudFormation** | Infrastructure as code management | Stack operations, template management |
| **AWS Pricing** | Real-time service pricing information | Price lookups, cost estimation |

## 📡 API Endpoints

### Core Endpoints
- `GET /` - Health check
- `GET /servers` - List all servers
- `POST /servers` - Add new server
- `GET /tools` - List all available tools
- `POST /chat` - Chat with Bedrock using tools

### Management Endpoints
- `GET /servers/{name}` - Get server details
- `POST /servers/{name}/reconnect` - Reconnect server
- `GET /debug/connections` - Debug connection states

## 💬 Example Usage

### Chat with Tools
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are my AWS costs for this month?",
    "use_tools": true
  }'
```

### List Available Tools
```bash
curl http://localhost:8000/tools
```

### Check Server Status
```bash
curl http://localhost:8000/servers
```

## ⚙️ Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
# AWS Configuration
AWS_PROFILE=default
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=your_key
AWS_SECRET_ACCESS_KEY=your_secret

# Bedrock Configuration  
BEDROCK_MODEL_ID=anthropic.claude-3-5-sonnet-20241022-v2:0

# Server Configuration
AUTO_CONFIGURE_SERVERS=true
```

### Manual Server Configuration
```bash
# Configure servers manually
python configure_servers.py setup

# List configured servers
python configure_servers.py list
```

## 🔧 Troubleshooting

### Common Issues

1. **Server connection failed**
   - Check UV installation: `uv --version`
   - Verify AWS credentials: `aws configure list`
   - Check logs for specific errors

2. **Tools not available**
   - Check server status: `curl http://localhost:8000/servers`
   - Reconnect: `curl -X POST http://localhost:8000/servers/{name}/reconnect`

3. **AWS credentials not found**
   - Run `aws configure` or set environment variables
   - Check `.env` file configuration

### Debug Information
```bash
curl http://localhost:8000/debug/connections
```

## 📚 Documentation

- [Detailed Setup Guide](SETUP_GUIDE.md) - Complete installation and configuration
- [API Documentation](http://localhost:8000/docs) - Interactive API docs (when server is running)

## 🔗 Requirements

- Python 3.8+
- UV package manager
- AWS CLI (optional but recommended)
- AWS credentials configured

## 📦 Dependencies

- FastAPI
- Uvicorn  
- Boto3
- MCP
- Pydantic
- Python-dotenv

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**Need help?** Check the [Setup Guide](SETUP_GUIDE.md) or run `python quick_start.py` for automated setup.
