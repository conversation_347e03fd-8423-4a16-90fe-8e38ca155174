# MCP Server Setup Guide

This guide will help you set up AWS MCP (Model Context Protocol) servers with your FastAPI application.

## Prerequisites

### 1. Install UV Package Manager
```bash
# Install UV package manager
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. Install AWS MCP Servers
```bash
# Install AWS MCP servers
uv tool install awslabs-cost-explorer-mcp-server@latest
uv tool install awslabs-cfn-mcp-server@latest  
uv tool install awslabs-aws-pricing-mcp-server@latest
```

### 3. Install Python Dependencies
```bash
# Install Python dependencies
pip install fastapi uvicorn boto3 python-dotenv pydantic mcp requests
```

## AWS Configuration

### Option A: AWS CLI Configuration (Recommended)
```bash
aws configure --profile default
# Enter your AWS Access Key ID, Secret Access Key, Region, and output format
```

### Option B: Environment Variables
1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` file with your AWS credentials:
```env
AWS_PROFILE=default
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=your_actual_access_key_here
AWS_SECRET_ACCESS_KEY=your_actual_secret_key_here
BEDROCK_MODEL_ID=anthropic.claude-3-5-sonnet-20241022-v2:0
```

## Server Configuration Methods

### Method 1: Auto-Configuration (Recommended)

The servers will be configured automatically when the FastAPI app starts.

1. **Start your FastAPI server:**
```bash
python main.py
```

2. **Check the logs** to see if servers connected successfully:
```
✅ Successfully configured cost-explorer
✅ Successfully configured cloudformation  
✅ Successfully configured aws-pricing
🚀 Startup complete: 3/3 servers connected
```

### Method 2: Manual Configuration Script

1. **Start your FastAPI server:**
```bash
python main.py
```

2. **In another terminal, run the configuration script:**
```bash
python configure_servers.py setup
```

3. **Check server status:**
```bash
python configure_servers.py list
```

### Method 3: Individual API Calls

You can also configure servers individually using curl:

```bash
# Configure Cost Explorer
curl -X POST "http://localhost:8000/servers" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "cost-explorer",
    "command": "uv",
    "args": ["tool", "run", "--from", "awslabs-cost-explorer-mcp-server@latest", "awslabs-cost-explorer-mcp-server"],
    "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"},
    "description": "AWS Cost Explorer MCP Server",
    "enabled": true
  }'
```

## Testing Your Setup

### 1. Check Server Status
```bash
curl http://localhost:8000/servers
```

### 2. List Available Tools
```bash
curl http://localhost:8000/tools
```

### 3. Test Chat with Tools
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What AWS services can you help me with?", "use_tools": true}'
```

## Troubleshooting

### Common Issues

1. **Server connection failed:**
   - Check that UV tools are installed correctly
   - Verify AWS credentials are configured
   - Check the logs for specific error messages

2. **AWS credentials not found:**
   - Run `aws configure` to set up credentials
   - Or set environment variables in `.env` file

3. **Tools not available:**
   - Check server status: `curl http://localhost:8000/servers`
   - Reconnect servers: `curl -X POST http://localhost:8000/servers/{server_name}/reconnect`

### Debug Information
```bash
# Get detailed connection information
curl http://localhost:8000/debug/connections
```

## Environment Variables Reference

| Variable | Description | Default |
|----------|-------------|---------|
| `AWS_PROFILE` | AWS profile to use | `default` |
| `AWS_REGION` | AWS region | `ap-south-1` |
| `AWS_ACCESS_KEY_ID` | AWS access key | - |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | - |
| `BEDROCK_MODEL_ID` | Bedrock model ID | `anthropic.claude-3-5-sonnet-20241022-v2:0` |
| `AUTO_CONFIGURE_SERVERS` | Auto-configure servers on startup | `true` |
| `FASTMCP_LOG_LEVEL` | MCP server log level | `ERROR` |
| `API_HOST` | API host | `0.0.0.0` |
| `API_PORT` | API port | `8000` |
| `LOG_LEVEL` | Application log level | `info` |

## Available MCP Servers

1. **Cost Explorer** (`cost-explorer`)
   - Analyze AWS costs and billing
   - Generate cost reports
   - Track spending trends

2. **CloudFormation** (`cloudformation`)
   - Manage infrastructure as code
   - Deploy and update stacks
   - Monitor stack status

3. **AWS Pricing** (`aws-pricing`)
   - Get real-time service pricing
   - Compare pricing across regions
   - Calculate cost estimates

## Next Steps

Once your servers are configured and running:

1. Use the chat endpoint to interact with AWS services
2. Explore available tools via the `/tools` endpoint
3. Build custom integrations using the API endpoints
4. Monitor server health via the `/servers` endpoint
